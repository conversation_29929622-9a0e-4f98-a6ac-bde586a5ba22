<template>
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Pending Approvals</h1>
      <p class="text-gray-600 dark:text-gray-400">
        Review and approve charging stations submitted by users
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="spinner w-8 h-8"></div>
      <span class="ml-3 text-gray-600 dark:text-gray-400">Loading pending stations...</span>
    </div>

    <!-- Empty State -->
    <div v-else-if="!pendingStations.length" class="text-center py-12">
      <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Pending Approvals</h3>
      <p class="text-gray-600 dark:text-gray-400">All charging stations have been reviewed.</p>
    </div>

    <!-- Pending Stations List -->
    <div v-else class="space-y-6">
      <div
        v-for="station in pendingStations"
        :key="station.id"
        class="card p-6"
      >
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <!-- Station Info -->
          <div class="flex-1 mb-4 lg:mb-0">
            <div class="flex items-start justify-between mb-2">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                {{ station.name }}
              </h3>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                Pending
              </span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
              <div>
                <span class="font-medium">Submitted by:</span> {{ station.owner_name }} ({{ station.owner_email }})
              </div>
              <div>
                <span class="font-medium">Power Output:</span> {{ station.power_output }}kW
              </div>
              <div>
                <span class="font-medium">Connector Type:</span> {{ station.connector_type }}
              </div>
              <div>
                <span class="font-medium">Status:</span> {{ station.status }}
              </div>
              <div>
                <span class="font-medium">Location:</span> {{ station.latitude }}, {{ station.longitude }}
              </div>
              <div>
                <span class="font-medium">Submitted:</span> {{ formatDate(station.created_at) }}
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex space-x-3">
            <button
              @click="viewOnMap(station)"
              class="btn btn-secondary btn-sm"
              title="View on Map"
            >
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
              </svg>
              Map
            </button>
            <button
              @click="approveStation(station.id)"
              :disabled="actionLoading[station.id]"
              class="btn bg-green-600 hover:bg-green-700 text-white btn-sm"
            >
              <svg v-if="actionLoading[station.id]" class="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              Approve
            </button>
            <button
              @click="rejectStation(station.id)"
              :disabled="actionLoading[station.id]"
              class="btn bg-red-600 hover:bg-red-700 text-white btn-sm"
            >
              <svg v-if="actionLoading[station.id]" class="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
              Reject
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { adminAPI } from '@/services/api'

const router = useRouter()
const pendingStations = ref([])
const loading = ref(false)
const actionLoading = reactive({})

const fetchPendingStations = async () => {
  try {
    loading.value = true
    const response = await adminAPI.getPendingStations()
    if (response.success) {
      pendingStations.value = response.data.stations
    }
  } catch (error) {
    console.error('Failed to fetch pending stations:', error)
  } finally {
    loading.value = false
  }
}

const approveStation = async (stationId) => {
  try {
    actionLoading[stationId] = true
    const response = await adminAPI.approveStation(stationId)
    if (response.success) {
      // Remove from pending list
      pendingStations.value = pendingStations.value.filter(s => s.id !== stationId)
    }
  } catch (error) {
    console.error('Failed to approve station:', error)
  } finally {
    actionLoading[stationId] = false
  }
}

const rejectStation = async (stationId) => {
  try {
    actionLoading[stationId] = true
    const response = await adminAPI.rejectStation(stationId)
    if (response.success) {
      // Remove from pending list
      pendingStations.value = pendingStations.value.filter(s => s.id !== stationId)
    }
  } catch (error) {
    console.error('Failed to reject station:', error)
  } finally {
    actionLoading[stationId] = false
  }
}

const viewOnMap = (station) => {
  router.push(`/map?lat=${station.latitude}&lng=${station.longitude}`)
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  fetchPendingStations()
})
</script>
