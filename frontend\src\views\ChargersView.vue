<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          ⚡ Charging Stations
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Manage your electric vehicle charging stations network
        </p>
      </div>
      <div v-if="!authStore.isGuest" class="flex-shrink-0">
        <router-link
          to="/chargers/new"
          class="btn btn-primary btn-lg inline-flex items-center group"
        >
          <svg class="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
          </svg>
          Add New Station
          <div class="ml-2 w-2 h-2 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </router-link>
      </div>
      <!-- Guest mode indicator -->
      <div v-else class="flex-shrink-0">
        <div class="px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            👁️ Viewing as Guest (Read-only)
          </span>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
            <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Stations</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ chargersStore.totalChargers }}</p>
          </div>
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
            <svg class="w-6 h-6 text-success-600 dark:text-success-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ chargersStore.activeChargers.length }}</p>
          </div>
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
            <svg class="w-6 h-6 text-warning-600 dark:text-warning-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ chargersStore.inactiveChargers.length }}</p>
          </div>
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Power</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ averagePower }}kW</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card p-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filters</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="label">Status</label>
          <select v-model="filters.status" class="input">
            <option value="">All Status</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>
        </div>

        <div>
          <label class="label">Min Power Output (kW)</label>
          <input
            v-model="filters.power_output"
            type="number"
            placeholder="e.g. 50"
            class="input"
          />
        </div>

        <div>
          <label class="label">Connector Type</label>
          <select v-model="filters.connector_type" class="input">
            <option value="">All Types</option>
            <option value="Type 1">Type 1</option>
            <option value="Type 2">Type 2</option>
            <option value="Type 3">Type 3</option>
            <option value="CCS">CCS</option>
            <option value="CHAdeMO">CHAdeMO</option>
          </select>
        </div>

        <div class="flex items-end space-x-2">
          <button @click="applyFilters" class="btn btn-primary">
            Apply Filters
          </button>
          <button @click="clearFilters" class="btn btn-secondary">
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="chargersStore.loading" class="flex justify-center py-12">
      <div class="spinner w-8 h-8"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="chargersStore.error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <p class="ml-3 text-sm text-red-800 dark:text-red-200">
          {{ chargersStore.error }}
        </p>
      </div>
    </div>

    <!-- Chargers Grid -->
    <div v-else-if="chargersStore.filteredChargers.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <ChargerCard
        v-for="charger in chargersStore.filteredChargers"
        :key="charger.id"
        :charger="charger"
        @edit="editCharger"
        @delete="deleteCharger"
      />
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No charging stations found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        {{ authStore.isGuest ? 'No charging stations available to view.' : (hasFilters ? 'Try adjusting your filters or' : 'Get started by') + ' creating a new charging station.' }}
      </p>
      <div v-if="!authStore.isGuest" class="mt-6">
        <router-link to="/chargers/new" class="btn btn-primary">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
          </svg>
          Add New Station
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useChargersStore } from '@/stores/chargers'
import { useAuthStore } from '@/stores/counter'
import ChargerCard from '@/components/ChargerCard.vue'

const router = useRouter()
const chargersStore = useChargersStore()
const authStore = useAuthStore()

const filters = reactive({
  status: '',
  power_output: '',
  connector_type: ''
})

const hasFilters = computed(() => {
  return filters.status || filters.power_output || filters.connector_type
})

const averagePower = computed(() => {
  if (chargersStore.chargers.length === 0) return 0
  const total = chargersStore.chargers.reduce((sum, charger) => sum + charger.power_output, 0)
  return Math.round(total / chargersStore.chargers.length)
})

const applyFilters = () => {
  chargersStore.setFilters(filters)
}

const clearFilters = () => {
  filters.status = ''
  filters.power_output = ''
  filters.connector_type = ''
  chargersStore.clearFilters()
}

const editCharger = (charger) => {
  router.push(`/chargers/${charger.id}/edit`)
}

const deleteCharger = async (charger) => {
  if (confirm(`Are you sure you want to delete "${charger.name}"?`)) {
    const result = await chargersStore.deleteCharger(charger.id)
    if (result.success) {
      // Optionally show success message
    }
  }
}

onMounted(() => {
  chargersStore.fetchChargers()
})
</script>
